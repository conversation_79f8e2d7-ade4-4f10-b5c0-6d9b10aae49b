"""
化学配方计算器

负责化学配方的计算逻辑，不依赖UI层
"""

import pandas as pd
from typing import Dict, List, Tuple, Optional
from data import MaterialsDataManager
from business.formula_parser import FormulaParser


class FormulaCalculator:
    """化学配方计算核心类"""

    def __init__(self, db_file: str = None):
        # 使用材料数据管理器
        self.materials_manager = MaterialsDataManager(db_file)
        self.formula_parser = FormulaParser()

    def get_material_info(self, element: str) -> Tuple[str, float, float, float]:
        """从数据库中获取元素的原料信息，只使用被选中的原料"""
        # 使用材料管理器获取选中的原料信息
        material_data = self.materials_manager.get_material_by_element(element)

        if material_data:
            formula = material_data.get('Formula')
            molecular_weight = material_data.get('Mr')
            purity = material_data.get('Purity')
            excess = material_data.get('Excess')

            # 处理数据类型和验证必需字段
            try:
                # 验证化学式
                if formula == '' or pd.isna(formula) or formula is None:
                    raise ValueError(f"元素 {element} 缺少化学式数据")

                # 验证分子量
                if molecular_weight == '' or pd.isna(molecular_weight) or molecular_weight is None:
                    raise ValueError(f"元素 {element} 缺少分子量数据")
                else:
                    molecular_weight = float(molecular_weight)

                # 验证纯度
                if purity == '' or pd.isna(purity) or purity is None:
                    raise ValueError(f"元素 {element} 缺少纯度数据")
                else:
                    purity = float(purity)
                    # 纯度以百分比形式存储（如99.99表示99.99%），需要转换为小数
                    purity = purity / 100

                # 验证过量系数
                if excess == '' or pd.isna(excess) or excess is None:
                    raise ValueError(f"元素 {element} 缺少过量系数数据")
                else:
                    excess = float(excess)

            except ValueError as ve:
                raise ve
            except:
                raise ValueError(f"元素 {element} 的数据格式错误")

            return formula, molecular_weight, purity, excess

        # 如果找不到被选中的元素，抛出错误
        raise ValueError(f"未找到元素 {element} 的原料数据，请在设置中添加该元素的原料信息")

    def calculate_raw_materials(self, element_moles: Dict[str, float], target_mass: float) -> Dict[str, float]:
        """计算原料用量"""
        try:
            raw_material_masses = {}
            total_molar_mass = 0

            for element, moles in element_moles.items():
                if element == 'O':  # 跳过氧元素
                    continue

                # 从数据库中获取原料信息
                formula, molecular_weight, purity, excess = self.get_material_info(element)

                if molecular_weight <= 0:
                    raise ValueError(f"元素 {element} 的分子量无效: {molecular_weight}")

                # 计算质量
                mass = moles * molecular_weight / purity * excess
                raw_material_masses[formula] = mass
                total_molar_mass += mass

            # 归一化到目标质量
            if total_molar_mass == 0:
                raise ValueError("计算出的总质量为0，请检查化学式和原料设置")

            scale_factor = target_mass / total_molar_mass
            return {k: v * scale_factor for k, v in raw_material_masses.items()}

        except Exception as e:
            raise ValueError(f"原料计算错误: {str(e)}")

    def get_material_details(self, element_moles: Dict[str, float]) -> Dict[str, Dict]:
        """获取原料的详细信息"""
        material_details = {}

        for element, moles in element_moles.items():
            if element == 'O':  # 跳过氧元素
                continue

            # 从数据库中获取原料信息（用于计算）
            formula, molecular_weight, purity_decimal, excess = self.get_material_info(element)

            # 获取原始材料数据（用于显示）
            material_data = self.materials_manager.get_material_by_element(element)
            original_purity = material_data.get('Purity', purity_decimal * 100) if material_data else purity_decimal * 100

            material_details[formula] = {
                'Formula': formula,
                'Excess': excess,
                'Mr': molecular_weight,
                'Purity': original_purity,  # 显示原始百分比值
                'Element': element
            }

        return material_details

    def calculate_single_formula(self, formula: str, target_mass: float) -> Dict:
        """计算单个化学式的配料"""
        try:
            # 检查是否有原料数据
            if not self.materials_manager.get_materials_data():
                raise ValueError("没有找到原料数据！请先加载原料数据文件。")

            # 解析化学式
            element_moles = self.formula_parser.parse_chemical_formula(formula)

            # 计算原料用量和详细信息
            raw_material_masses = self.calculate_raw_materials(element_moles, target_mass)
            material_details = self.get_material_details(element_moles)

            return {
                'formula': formula,
                'target_mass': target_mass,
                'materials': raw_material_masses,
                'material_details': material_details,
                'element_moles': element_moles
            }

        except Exception as e:
            raise ValueError(f"计算错误: {str(e)}")

    def calculate_multiple_formulas(self, formulas_and_masses: List[Tuple[str, float]]) -> Dict:
        """计算多个化学式的配料"""
        try:
            # 检查是否有原料数据
            if not self.materials_manager.get_materials_data():
                raise ValueError("没有找到原料数据！请先加载原料数据文件。")

            # 计算每个化学式的配料
            all_results = []
            all_materials = set()

            for formula, target_mass in formulas_and_masses:
                result = self.calculate_single_formula(formula, target_mass)
                all_materials.update(result['materials'].keys())
                all_results.append(result)

            return {
                'results': all_results,
                'all_materials': sorted(list(all_materials))
            }

        except Exception as e:
            raise ValueError(f"计算错误: {str(e)}")

    def validate_formula(self, formula: str) -> bool:
        """验证化学式格式"""
        return self.formula_parser.validate_formula(formula)

    def get_available_elements(self) -> List[str]:
        """获取可用的元素列表"""
        try:
            materials = self.materials_manager.get_selected_materials()
            elements = [material.get('Element', '') for material in materials]
            return [elem for elem in elements if elem]
        except Exception as e:
            print(f"获取可用元素失败: {e}")
            return []

    def get_materials_summary(self) -> Dict:
        """获取材料数据摘要"""
        try:
            all_materials = self.materials_manager.get_materials_data()
            selected_materials = self.materials_manager.get_selected_materials()
            
            return {
                'total_materials': len(all_materials),
                'selected_materials': len(selected_materials),
                'available_elements': len(set(material.get('Element', '') for material in selected_materials))
            }
        except Exception as e:
            print(f"获取材料摘要失败: {e}")
            return {'total_materials': 0, 'selected_materials': 0, 'available_elements': 0}
