"""
单任务页面组件

处理单个化学式的计算
"""

import ttkbootstrap as ttk
import pandas as pd
from datetime import datetime
from typing import Optional, Dict
from ui.utils.styles import StyleManager
from ui.utils.dialogs import DialogUtils
from utils.constants import TABLE_COLUMNS, COLUMN_WIDTHS


class SingleTaskTab:
    """单任务计算选项卡组件"""

    def __init__(self, parent, calculator):
        self.parent = parent
        self.calculator = calculator
        self.frame = ttk.Frame(parent)
        self.calculation_result = None
        self.create_widgets()

    def create_widgets(self):
        """创建单任务页面组件"""
        # 配置网格权重
        self.frame.grid_rowconfigure(0, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.frame)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=30)
        main_frame.grid_rowconfigure(1, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 输入区域
        self.create_input_section(main_frame)

        # 结果区域
        self.create_result_section(main_frame)

    def create_input_section(self, parent):
        """创建输入区域"""
        # 确保样式已配置
        StyleManager.setup_modern_labelframe_style()
        StyleManager.setup_button_styles()

        input_frame = ttk.LabelFrame(
            parent,
            text="化学式输入",
            style='Modern.TLabelframe',
            padding=25
        )
        input_frame.grid(row=0, column=0, sticky="ew", pady=(0, 20))
        input_frame.grid_columnconfigure(1, weight=1)

        # 化学式输入
        ttk.Label(
            input_frame,
            text="化学式",
            font=('Microsoft YaHei', 10)
        ).grid(row=0, column=0, sticky="w", pady=(0, 20))

        self.formula_entry = ttk.Entry(
            input_frame,
            font=('Microsoft YaHei', 10),
            width=60
        )
        self.formula_entry.grid(row=0, column=1, sticky="ew", padx=(20, 0), pady=(0, 20)) 

        # 总质量输入
        ttk.Label(
            input_frame,
            text="总质量(g)",
            font=('Microsoft YaHei', 10)
        ).grid(row=1, column=0, sticky="w", pady=(0, 20))

        self.mass_entry = ttk.Entry(
            input_frame,
            font=('Microsoft YaHei', 10),
            width=60
        )
        self.mass_entry.grid(row=1, column=1, sticky="ew", padx=(20, 0), pady=(0, 20))

        # 按钮区域
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky="ew")

        # 按钮容器 - 使用grid布局实现均匀分布
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(fill="x", expand=True)

        # 配置列权重，实现均匀分布
        for i in range(3):
            buttons_container.grid_columnconfigure(i, weight=1)

        # 创建三个按钮
        self.clear_btn = ttk.Button(
            buttons_container,
            text="清空",
            command=self.clear_inputs,
            bootstyle="warning-outline",
            width=10,
            padding=(10, 15)
        )
        self.clear_btn.grid(row=0, column=0, sticky="ew", padx=2)

        self.calculate_btn = ttk.Button(
            buttons_container,
            text="计算",
            command=self.calculate_formula,
            bootstyle="primary-outline",
            width=10,
            padding=(10, 15)
        )
        self.calculate_btn.grid(row=0, column=1, sticky="ew", padx=2)

        self.download_btn = ttk.Button(
            buttons_container,
            text="下载",
            command=self.export_calculation_result,
            bootstyle="success-outline",
            width=10,
            padding=(10, 15)
        )
        self.download_btn.grid(row=0, column=2, sticky="ew", padx=2)

    def create_result_section(self, parent):
        """创建结果区域"""
        result_frame = ttk.LabelFrame(
            parent,
            text="计算结果",
            style='Modern.TLabelframe',
            padding=25
        )
        result_frame.grid(row=1, column=0, sticky="nsew")
        result_frame.grid_rowconfigure(0, weight=1)
        result_frame.grid_columnconfigure(0, weight=1)

        # 结果容器
        self.result_container = ttk.Frame(result_frame)
        self.result_container.grid(row=0, column=0, sticky="nsew")
        self.result_container.grid_rowconfigure(0, weight=1)
        self.result_container.grid_columnconfigure(0, weight=1)

        # 初始提示
        self.show_initial_message()

    def show_initial_message(self):
        """显示初始提示"""
        # 清空容器
        for widget in self.result_container.winfo_children():
            widget.destroy()

        # 提示信息
        message_frame = ttk.Frame(self.result_container)
        message_frame.grid(row=0, column=0, sticky="nsew")

        ttk.Label(
            message_frame,
            text="💡 请输入化学式和总质量，然后点击\"开始计算\"",
            font=('Microsoft YaHei', 16, 'bold'),
            foreground='#FF770F',
            anchor="center"
        ).pack(expand=True)

    def clear_inputs(self):
        """清空输入框"""
        self.formula_entry.delete(0, 'end')
        self.mass_entry.delete(0, 'end')
        # 清空结果并显示初始提示
        self.calculation_result = None
        self.show_initial_message()

    def calculate_formula(self):
        """执行化学式计算"""
        try:
            # 获取输入
            formula = self.formula_entry.get().strip()
            if not formula:
                DialogUtils.show_error("输入错误", "请输入化学式")
                return

            try:
                mass = float(self.mass_entry.get().strip())
                if mass <= 0:
                    raise ValueError
            except ValueError:
                DialogUtils.show_error("输入错误", "总质量必须是正数")
                return

            # 计算
            result = self.calculator.calculate_single_formula(formula, mass)
            self.calculation_result = result

            # 显示结果
            self.display_calculation_result(result)

        except Exception as e:
            DialogUtils.show_error("计算错误", str(e))

    def display_calculation_result(self, result):
        """显示计算结果"""
        # 清空容器
        for widget in self.result_container.winfo_children():
            widget.destroy()

        # 创建结果表格
        self.create_result_table(result)

    def create_result_table(self, result):
        """创建结果表格"""
        # 确保样式已配置
        StyleManager.setup_table_styles()

        # 结果表格容器
        table_frame = ttk.Frame(self.result_container)
        table_frame.grid(row=0, column=0, sticky="nsew", pady=(0, 15))
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 创建结果表格
        columns = TABLE_COLUMNS['single_result']
        self.result_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=12
        )

        # 设置列
        for col in columns:
            self.result_tree.heading(col, text=col)
            width = COLUMN_WIDTHS.get(col, 150)
            anchor = "w" if col == "原料名称" else "center"
            self.result_tree.column(col, width=width, anchor=anchor)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=scrollbar.set)

        # 填充数据
        self.populate_result_table(result)

        # 布局
        self.result_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")

    def populate_result_table(self, result):
        """填充结果表格数据"""
        materials = result['materials']
        material_details = result.get('material_details', {})

        for i, material_formula in enumerate(sorted(materials.keys())):
            mass = materials[material_formula]
            details = material_details.get(material_formula, {})

            # 获取材料详细信息
            formula = details.get('Formula', material_formula)
            excess = details.get('Excess', 'N/A')
            mr = details.get('Mr', 'N/A')
            purity = details.get('Purity', 'N/A')

            # 格式化数值显示
            excess_str = f"{excess:.3f}" if isinstance(excess, (int, float)) else str(excess)
            mr_str = f"{mr:.3f}" if isinstance(mr, (int, float)) else str(mr)
            purity_str = f"{purity:.3f}" if isinstance(purity, (int, float)) else str(purity)
            mass_str = f"{mass:.4f}"

            item = self.result_tree.insert("", "end", values=[
                formula,
                excess_str,
                mr_str,
                purity_str,
                mass_str
            ])
            # 设置隔行变色
            tag = 'evenrow' if i % 2 == 0 else 'oddrow'
            self.result_tree.item(item, tags=(tag,))

        # 配置隔行变色
        StyleManager.configure_treeview_colors(self.result_tree)

    def export_calculation_result(self):
        """导出计算结果"""
        if not self.calculation_result:
            DialogUtils.show_error("导出错误", "请先进行计算")
            return

        try:
            file_path = DialogUtils.get_save_file_path("单个化学式计算结果")
            if not file_path:
                return

            self.export_result_to_excel(self.calculation_result, file_path)
            DialogUtils.show_info("导出成功", f"结果已保存到:\n{file_path}")

        except Exception as e:
            DialogUtils.show_error("导出错误", f"导出时出错: {str(e)}")

    def export_result_to_excel(self, result, file_path):
        """将结果导出到Excel"""
        # 创建数据
        data = []
        materials = result['materials']
        material_details = result.get('material_details', {})

        for material_formula in sorted(materials.keys()):
            mass = materials[material_formula]
            details = material_details.get(material_formula, {})

            # 获取材料详细信息
            formula = details.get('Formula', material_formula)
            excess = details.get('Excess', 'N/A')
            mr = details.get('Mr', 'N/A')
            purity = details.get('Purity', 'N/A')

            data.append({
                'Formula': formula,
                'Excess': excess if isinstance(excess, (int, float)) else excess,
                'Mr': mr if isinstance(mr, (int, float)) else mr,
                'Purity': purity if isinstance(purity, (int, float)) else purity,
                'Mass': round(mass, 4)
            })

        df = pd.DataFrame(data)

        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='计算结果', index=False)

            # 添加计算信息
            info_data = [{
                '化学式': result['formula'],
                '目标质量(g)': result['target_mass'],
                '计算时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }]
            info_df = pd.DataFrame(info_data)
            info_df.to_excel(writer, sheet_name='计算信息', index=False)

    def get_frame(self):
        """获取组件框架"""
        return self.frame
